import { useState, useEffect } from "react";
import {
  DownloadIcon,
  MailIcon,
  RefreshCwIcon,
  CopyIcon,
  CheckIcon,
  AlertCircleIcon,
} from "lucide-react";
import { QRCodeService } from "../services/qrCodeService";
import type { Attendee, Event } from "../types";

interface QRCodeDisplayProps {
  attendee: Attendee;
  event: Event;
  size?: "small" | "medium" | "large";
  showActions?: boolean;
  onEmailSent?: () => void;
}

const QRCodeDisplay = ({
  attendee,
  event,
  size = "medium",
  showActions = true,
  onEmailSent,
}: QRCodeDisplayProps) => {
  const [qrCodeImage, setQrCodeImage] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [copied, setCopied] = useState(false);
  const [isGeneratingEmail, setIsGeneratingEmail] = useState(false);

  const sizeMap = {
    small: { width: 128, containerClass: "w-32 h-32" },
    medium: { width: 256, containerClass: "w-64 h-64" },
    large: { width: 384, containerClass: "w-96 h-96" },
  };

  const currentSize = sizeMap[size];

  useEffect(() => {
    generateQRCode();
  }, [attendee.id, event.id]);

  const generateQRCode = async () => {
    setIsLoading(true);
    setError("");

    try {
      const dataUrl = await QRCodeService.generateQRCodeImage(
        attendee.id,
        event.id,
        {
          width: currentSize.width,
          margin: 2,
          color: {
            dark: "#1f2937", // gray-800
            light: "#ffffff",
          },
        }
      );
      setQrCodeImage(dataUrl);
    } catch (err) {
      setError("Failed to generate QR code");
      console.error("QR Code generation error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = () => {
    if (qrCodeImage) {
      const filename = `qr-code-${event.name.replace(
        /\s+/g,
        "-"
      )}-${attendee.name.replace(/\s+/g, "-")}.png`;
      QRCodeService.downloadQRCode(qrCodeImage, filename);
    }
  };

  const handleCopyToClipboard = async () => {
    if (qrCodeImage) {
      try {
        // Convert data URL to blob
        const response = await fetch(qrCodeImage);
        const blob = await response.blob();

        // Copy to clipboard
        await navigator.clipboard.write([
          new ClipboardItem({ "image/png": blob }),
        ]);

        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error("Failed to copy to clipboard:", err);
        // Fallback: copy the data URL as text
        try {
          await navigator.clipboard.writeText(qrCodeImage);
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        } catch (fallbackErr) {
          console.error("Fallback copy failed:", fallbackErr);
        }
      }
    }
  };

  const handleSendEmail = async () => {
    setIsGeneratingEmail(true);

    try {
      const emailData = await QRCodeService.generateEmailQRCode(
        attendee.id,
        event.id,
        attendee.name,
        event.name
      );

      // In a real application, this would send an actual email
      // For demo purposes, we'll simulate the email sending
      console.log("Email data generated:", emailData);

      // Simulate email sending delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Show success message
      alert(
        `QR code email would be sent to ${attendee.email}\n\nSubject: ${emailData.emailSubject}`
      );

      if (onEmailSent) {
        onEmailSent();
      }
    } catch (err) {
      console.error("Failed to generate email:", err);
      alert("Failed to send email. Please try again.");
    } finally {
      setIsGeneratingEmail(false);
    }
  };

  if (isLoading) {
    return (
      <div
        className={`${currentSize.containerClass} flex items-center justify-center bg-gray-100 rounded-lg`}
      >
        <div className="text-center">
          <RefreshCwIcon className="h-8 w-8 text-gray-400 animate-spin mx-auto mb-2" />
          <p className="text-sm text-gray-500">Generating QR code...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`${currentSize.containerClass} flex items-center justify-center bg-red-50 rounded-lg border border-red-200`}
      >
        <div className="text-center">
          <AlertCircleIcon className="h-8 w-8 text-red-400 mx-auto mb-2" />
          <p className="text-sm text-red-600">{error}</p>
          <button
            onClick={generateQRCode}
            className="mt-2 text-xs text-red-600 hover:text-red-800 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* QR Code Image */}
      <div
        className={`${currentSize.containerClass} mx-auto bg-white p-4 rounded-lg shadow-sm border`}
      >
        <img
          src={qrCodeImage}
          alt={`QR Code for ${attendee.name}`}
          className="w-full h-full object-contain"
        />
      </div>

      {/* Attendee Info */}
      <div className="text-center">
        <h4 className="font-medium text-gray-900">{attendee.name}</h4>
        <p className="text-sm text-gray-500">{attendee.email}</p>
        <p className="text-xs text-gray-400 mt-1">
          QR Code ID: {attendee.qrCode}
        </p>
      </div>

      {/* Action Buttons */}
      {showActions && (
        <div className="flex justify-center space-x-2">
          <button
            onClick={handleDownload}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            title="Download QR Code"
          >
            <DownloadIcon className="h-4 w-4 mr-1" />
            Download
          </button>

          <button
            onClick={handleCopyToClipboard}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            title="Copy to Clipboard"
          >
            {copied ? (
              <>
                <CheckIcon className="h-4 w-4 mr-1 text-green-600" />
                Copied!
              </>
            ) : (
              <>
                <CopyIcon className="h-4 w-4 mr-1" />
                Copy
              </>
            )}
          </button>

          <button
            onClick={handleSendEmail}
            disabled={isGeneratingEmail}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Send via Email"
          >
            {isGeneratingEmail ? (
              <>
                <RefreshCwIcon className="h-4 w-4 mr-1 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <MailIcon className="h-4 w-4 mr-1" />
                Email
              </>
            )}
          </button>

          <button
            onClick={generateQRCode}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            title="Regenerate QR Code"
          >
            <RefreshCwIcon className="h-4 w-4 mr-1" />
            Refresh
          </button>
        </div>
      )}

      {/* QR Code Info */}
      <div className="text-center text-xs text-gray-500 space-y-1">
        <p>This QR code is unique and encrypted</p>
        <p>Valid for 24 hours from generation</p>
        <p>Present at event entrance for check-in</p>
      </div>
    </div>
  );
};

export default QRCodeDisplay;
