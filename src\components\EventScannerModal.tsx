import { useState, useEffect } from "react";
import {
  QrC<PERSON>,
  CheckCircle,
  XCircle,
  User,
  Al<PERSON><PERSON>riangle,
  Clock,
} from "lucide-react";
import BaseModal from "./BaseModal";
import QRScanner from "./QRScanner";
import type { ScanResult } from "../services/qrScannerService";
import { useEventStore } from "../store/eventStore";
import type { Event, Attendee } from "../types";
import { getModalZIndex } from "../utils/zIndex";

interface EventScannerModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: Event | null;
}

interface EventCheckInResult {
  success: boolean;
  attendee?: Attendee;
  error?: string;
  timestamp: Date;
}

const EventScannerModal = ({
  isOpen,
  onClose,
  event,
}: EventScannerModalProps) => {
  const { attendees, checkInAttendee, getEventAttendees } = useEventStore();
  const [isScanning, setIsScanning] = useState(true);
  const [checkInHistory, setCheckInHistory] = useState<EventCheckInResult[]>(
    []
  );
  const [currentResult, setCurrentResult] = useState<EventCheckInResult | null>(
    null
  );
  const [stats, setStats] = useState({
    totalScans: 0,
    successfulCheckIns: 0,
    failedScans: 0,
  });

  useEffect(() => {
    if (isOpen) {
      setIsScanning(true);
      setCheckInHistory([]);
      setCurrentResult(null);
      setStats({ totalScans: 0, successfulCheckIns: 0, failedScans: 0 });
    }
  }, [isOpen]);

  if (!isOpen || !event) return null;

  const eventAttendees = getEventAttendees(event.id);
  const checkedInCount = eventAttendees.filter((a) => a.checkedIn).length;

  const handleScan = async (scanResult: ScanResult) => {
    const timestamp = new Date();

    // Update scan statistics
    setStats((prev) => ({
      ...prev,
      totalScans: prev.totalScans + 1,
      failedScans: scanResult.success ? prev.failedScans : prev.failedScans + 1,
    }));

    if (!scanResult.success) {
      const failedResult: EventCheckInResult = {
        success: false,
        error: scanResult.error || "Invalid QR code",
        timestamp,
      };

      setCurrentResult(failedResult);
      setCheckInHistory((prev) => [failedResult, ...prev.slice(0, 9)]); // Keep last 10
      return;
    }

    try {
      // Check if QR code is for this event
      if (scanResult.eventId !== event.id) {
        const wrongEventResult: EventCheckInResult = {
          success: false,
          error: "QR code is not for this event",
          timestamp,
        };

        setCurrentResult(wrongEventResult);
        setCheckInHistory((prev) => [wrongEventResult, ...prev.slice(0, 9)]);
        return;
      }

      // Find the attendee
      const attendee = attendees.find((a) => a.id === scanResult.attendeeId);

      if (!attendee) {
        const notFoundResult: EventCheckInResult = {
          success: false,
          error: "Attendee not found in system",
          timestamp,
        };

        setCurrentResult(notFoundResult);
        setCheckInHistory((prev) => [notFoundResult, ...prev.slice(0, 9)]);
        return;
      }

      // Check if already checked in
      if (attendee.checkedIn) {
        const alreadyCheckedResult: EventCheckInResult = {
          success: false,
          attendee,
          error: `${attendee.name} is already checked in`,
          timestamp,
        };

        setCurrentResult(alreadyCheckedResult);
        setCheckInHistory((prev) => [
          alreadyCheckedResult,
          ...prev.slice(0, 9),
        ]);
        return;
      }

      // Perform check-in
      await checkInAttendee(attendee.id);

      // Success result
      const successResult: EventCheckInResult = {
        success: true,
        attendee,
        timestamp,
      };

      setCurrentResult(successResult);
      setCheckInHistory((prev) => [successResult, ...prev.slice(0, 9)]);

      // Update stats
      setStats((prev) => ({
        ...prev,
        successfulCheckIns: prev.successfulCheckIns + 1,
      }));
    } catch (error) {
      const errorResult: EventCheckInResult = {
        success: false,
        error: "Failed to check in attendee",
        timestamp,
      };

      setCurrentResult(errorResult);
      setCheckInHistory((prev) => [errorResult, ...prev.slice(0, 9)]);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const getResultIcon = (result: EventCheckInResult) => {
    if (result.success) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Event Scanner"
      subtitle={event.name}
      icon={<QrCode className="h-6 w-6 text-purple-600" />}
      size="6xl"
      zIndex={getModalZIndex("EVENT_SCANNER_MODAL")}
    >
      {/* Event Stats */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-blue-600">
            {eventAttendees.length}
          </div>
          <div className="text-xs text-blue-600">Total</div>
        </div>
        <div className="bg-green-50 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-green-600">
            {checkedInCount}
          </div>
          <div className="text-xs text-green-600">Checked In</div>
        </div>
        <div className="bg-purple-50 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-purple-600">
            {stats.successfulCheckIns}
          </div>
          <div className="text-xs text-purple-600">Scanned</div>
        </div>
        <div className="bg-red-50 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-red-600">
            {stats.failedScans}
          </div>
          <div className="text-xs text-red-600">Failed</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Scanner */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">QR Scanner</h4>
            <button
              onClick={() => setIsScanning(!isScanning)}
              className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white ${
                isScanning
                  ? "bg-red-600 hover:bg-red-700"
                  : "bg-green-600 hover:bg-green-700"
              }`}
            >
              {isScanning ? "Stop Scanner" : "Start Scanner"}
            </button>
          </div>

          {isScanning ? (
            <div className="aspect-square">
              <QRScanner
                onScan={handleScan}
                isActive={isScanning}
                className="w-full h-full"
              />
            </div>
          ) : (
            <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <QrCode className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Scanner stopped</p>
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        <div className="space-y-4">
          {/* Current Result */}
          {currentResult && (
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-3">
                Latest Scan
              </h4>
              <div
                className={`p-4 rounded-lg ${
                  currentResult.success
                    ? "bg-green-50 border border-green-200"
                    : "bg-red-50 border border-red-200"
                }`}
              >
                <div className="flex items-start">
                  {getResultIcon(currentResult)}
                  <div className="ml-3 flex-1">
                    <h5
                      className={`text-sm font-medium ${
                        currentResult.success
                          ? "text-green-800"
                          : "text-red-800"
                      }`}
                    >
                      {currentResult.success
                        ? "Check-in Successful!"
                        : "Check-in Failed"}
                    </h5>

                    {currentResult.attendee && (
                      <div className="mt-2">
                        <div className="flex items-center text-sm text-gray-600">
                          <User className="h-4 w-4 mr-2" />
                          {currentResult.attendee.name}
                        </div>
                        <div className="text-sm text-gray-500 mt-1">
                          {currentResult.attendee.email}
                        </div>
                      </div>
                    )}

                    {currentResult.error && (
                      <div className="mt-2">
                        <div className="flex items-center text-sm text-red-600">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          {currentResult.error}
                        </div>
                      </div>
                    )}

                    <div className="mt-2 flex items-center text-xs text-gray-500">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatTime(currentResult.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Scan History */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-3">
              Recent Scans
            </h4>
            <div className="max-h-64 overflow-y-auto space-y-2">
              {checkInHistory.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  <p className="text-sm">No scans yet</p>
                </div>
              ) : (
                checkInHistory.map((result, index) => (
                  <div
                    key={index}
                    className="flex items-center p-2 border border-gray-200 rounded"
                  >
                    {getResultIcon(result)}
                    <div className="ml-2 flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {result.attendee?.name || "Unknown"}
                        </p>
                        <span className="text-xs text-gray-500">
                          {formatTime(result.timestamp)}
                        </span>
                      </div>
                      {result.error && (
                        <p className="text-xs text-red-600 truncate">
                          {result.error}
                        </p>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end mt-6">
        <button
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Close
        </button>
      </div>
    </BaseModal>
  );
};

export default EventScannerModal;
