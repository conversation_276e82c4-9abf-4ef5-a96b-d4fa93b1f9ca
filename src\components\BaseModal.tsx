import { ReactNode, useEffect } from "react";
import { X } from "lucide-react";

interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  children: ReactNode;
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "6xl";
  zIndex?: number;
  showCloseButton?: boolean;
  closeOnBackdropClick?: boolean;
}

const BaseModal = ({
  isOpen,
  onClose,
  title,
  subtitle,
  icon,
  children,
  size = "lg",
  zIndex = 50,
  showCloseButton = true,
  closeOnBackdropClick = true,
}: BaseModalProps) => {
  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      // Only set overflow hidden if no other modals are open
      const existingModals = document.querySelectorAll('[data-modal="true"]');
      if (existingModals.length === 0) {
        document.body.style.overflow = "hidden";
      }
    } else {
      // Only restore overflow if this is the last modal closing
      const existingModals = document.querySelectorAll('[data-modal="true"]');
      if (existingModals.length <= 1) {
        document.body.style.overflow = "unset";
      }
    }

    // Cleanup on unmount
    return () => {
      const existingModals = document.querySelectorAll('[data-modal="true"]');
      if (existingModals.length <= 1) {
        document.body.style.overflow = "unset";
      }
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "max-w-xs sm:max-w-sm";
      case "md":
        return "max-w-sm sm:max-w-md";
      case "lg":
        return "max-w-md sm:max-w-lg";
      case "xl":
        return "max-w-lg sm:max-w-xl";
      case "2xl":
        return "max-w-xl sm:max-w-2xl";
      case "6xl":
        return "max-w-full sm:max-w-6xl";
      default:
        return "max-w-md sm:max-w-lg";
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && closeOnBackdropClick) {
      onClose();
    }
  };

  return (
    <div
      className={`fixed inset-0 overflow-y-auto`}
      style={{ zIndex: zIndex }}
      data-modal="true"
    >
      <div className="flex items-center justify-center min-h-screen pt-2 px-2 pb-4 text-center sm:pt-4 sm:px-4 sm:pb-20 sm:block sm:p-0">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={handleBackdropClick}
          aria-hidden="true"
        />

        {/* Center alignment helper */}
        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        {/* Modal panel */}
        <div
          className={`inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all w-full sm:my-8 sm:align-middle ${getSizeClasses()}`}
        >
          <div className="bg-white px-3 pt-4 pb-3 sm:px-4 sm:pt-5 sm:pb-4 lg:p-6 lg:pb-4">
            {/* Header */}
            {(title || showCloseButton) && (
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                {(title || icon) && (
                  <div className="flex items-center min-w-0 flex-1">
                    {icon && (
                      <div className="flex-shrink-0 flex items-center justify-center h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 rounded-full bg-blue-100">
                        {icon}
                      </div>
                    )}
                    {title && (
                      <div className={`${icon ? "ml-3 sm:ml-4" : ""} min-w-0`}>
                        <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900 truncate">
                          {title}
                        </h3>
                        {subtitle && (
                          <p className="text-xs sm:text-sm text-gray-500 truncate">
                            {subtitle}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                )}
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className="flex-shrink-0 ml-3 rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 p-1"
                    aria-label="Close modal"
                  >
                    <X className="h-5 w-5 sm:h-6 sm:w-6" />
                  </button>
                )}
              </div>
            )}

            {/* Content */}
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BaseModal;
