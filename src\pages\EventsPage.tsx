import { useState, useEffect } from "react";
import {
  Plus,
  Calendar,
  MapPin,
  Users,
  Edit,
  Trash,
  Eye,
  MoreVertical,
  QrCode,
  BarChart3,
  <PERSON>r<PERSON><PERSON><PERSON>,
} from "lucide-react";
import EventModal from "../components/EventModal";
import EventDetailsModal from "../components/EventDetailsModal";
import EventDashboardModal from "../components/EventDashboardModal";
import EventScannerModal from "../components/EventScannerModal";
import DoorlistModal from "../components/DoorlistModal";
import { useEventStore } from "../store/eventStore";
import type { Event } from "../types";

type EventTab = "ongoing" | "upcoming" | "completed";

const EventsPage = () => {
  const {
    events,
    isLoading,
    deleteEvent,
    setSelectedEvent,
    initializeMockData,
    getEventAttendees,
  } = useEventStore();

  const [activeTab, setActiveTab] = useState<EventTab>("ongoing");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isDashboardModalOpen, setIsDashboardModalOpen] = useState(false);
  const [isScannerModalOpen, setIsScannerModalOpen] = useState(false);
  const [isDoorlistModalOpen, setIsDoorlistModalOpen] = useState(false);
  const [selectedEvent, setSelectedEventLocal] = useState<Event | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);

  useEffect(() => {
    // Initialize with mock data if no events exist
    if (events.length === 0) {
      initializeMockData();
    }
  }, [events.length, initializeMockData]);

  // Filter events by tab
  const filteredEvents = events.filter((event) => {
    const now = new Date();
    const eventStart = new Date(event.startDate);
    const eventEnd = new Date(event.endDate);

    switch (activeTab) {
      case "ongoing":
        return (
          eventStart <= now && eventEnd >= now && event.status === "published"
        );
      case "upcoming":
        return (
          eventStart > now &&
          (event.status === "published" || event.status === "draft")
        );
      case "completed":
        return eventEnd < now || event.status === "completed";
      default:
        return true;
    }
  });

  const handleEditEvent = (event: Event) => {
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setIsEditModalOpen(true);
    setDropdownOpen(null);
  };

  const handleViewEvent = (event: Event) => {
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setIsDetailsModalOpen(true);
    setDropdownOpen(null);
  };

  const handleDashboardView = (event: Event) => {
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setIsDashboardModalOpen(true);
    setDropdownOpen(null);
  };

  const handleScannerView = (event: Event) => {
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setIsScannerModalOpen(true);
    setDropdownOpen(null);
  };

  const handleDoorlistView = (event: Event) => {
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setIsDoorlistModalOpen(true);
    setDropdownOpen(null);
  };

  const handleDeleteEvent = async (eventId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this event? This action cannot be undone."
      )
    ) {
      await deleteEvent(eventId);
    }
    setDropdownOpen(null);
  };

  const getStatusColor = (status: Event["status"]) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  };

  const tabs = [
    {
      id: "ongoing" as EventTab,
      name: "Đang diễn ra",
      count: events.filter((e) => {
        const now = new Date();
        const eventStart = new Date(e.startDate);
        const eventEnd = new Date(e.endDate);
        return eventStart <= now && eventEnd >= now && e.status === "published";
      }).length,
    },
    {
      id: "upcoming" as EventTab,
      name: "Sắp diễn ra",
      count: events.filter((e) => {
        const now = new Date();
        const eventStart = new Date(e.startDate);
        return (
          eventStart > now && (e.status === "published" || e.status === "draft")
        );
      }).length,
    },
    {
      id: "completed" as EventTab,
      name: "Đã diễn ra",
      count: events.filter((e) => {
        const now = new Date();
        const eventEnd = new Date(e.endDate);
        return eventEnd < now || e.status === "completed";
      }).length,
    },
  ];

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
        <div className="text-center sm:text-left">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
            Events
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your events and track attendance.
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full sm:w-auto"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Event
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav
            className="-mb-px flex space-x-4 sm:space-x-8 px-3 sm:px-6 overflow-x-auto"
            aria-label="Tabs"
          >
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`whitespace-nowrap py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm flex-shrink-0 ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <span className="hidden sm:inline">{tab.name}</span>
                <span className="sm:hidden">{tab.name.split(" ")[0]}</span>
                <span
                  className={`ml-1 sm:ml-2 py-0.5 px-1.5 sm:px-2.5 rounded-full text-xs ${
                    activeTab === tab.id
                      ? "bg-blue-100 text-blue-600"
                      : "bg-gray-100 text-gray-900"
                  }`}
                >
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-3 sm:p-6">
          {isLoading ? (
            <div className="text-center py-8 sm:py-12">
              <div className="inline-block animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-sm text-gray-500">Loading events...</p>
            </div>
          ) : filteredEvents.length === 0 ? (
            <div className="text-center py-8 sm:py-12">
              <Calendar className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">
                No events in this category
              </h3>
              <p className="text-sm sm:text-base text-gray-500 mb-4">
                {activeTab === "ongoing" && "No events are currently running."}
                {activeTab === "upcoming" && "No upcoming events scheduled."}
                {activeTab === "completed" && "No completed events found."}
              </p>
              {activeTab !== "completed" && (
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Event
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {filteredEvents.map((event) => {
                const attendees = getEventAttendees(event.id);
                const checkedInCount = attendees.filter(
                  (a) => a.checkedIn
                ).length;

                return (
                  <div
                    key={event.id}
                    className="bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:shadow-lg transition-shadow"
                  >
                    <div className="p-4 sm:p-6">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-2 sm:space-y-0">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-base sm:text-lg font-medium text-gray-900 truncate">
                            {event.name}
                          </h3>
                          <p className="mt-1 text-sm text-gray-500 line-clamp-2">
                            {event.description}
                          </p>
                        </div>
                        <span
                          className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium self-start sm:self-auto ${
                            event.status === "published"
                              ? "bg-green-100 text-green-800"
                              : event.status === "draft"
                              ? "bg-gray-100 text-gray-800"
                              : event.status === "completed"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {event.status}
                        </span>
                      </div>

                      <div className="mt-3 sm:mt-4 space-y-1 sm:space-y-2">
                        <div className="flex items-center text-xs sm:text-sm text-gray-500">
                          <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                          <span className="truncate">
                            {new Date(event.startDate).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center text-xs sm:text-sm text-gray-500">
                          <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                          <span className="truncate">{event.location}</span>
                        </div>
                        <div className="flex items-center text-xs sm:text-sm text-gray-500">
                          <Users className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                          <span>
                            {attendees.length} registered
                            {event.maxAttendees &&
                              ` / ${event.maxAttendees} max`}
                          </span>
                        </div>
                      </div>

                      <div className="mt-3 sm:mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                        <div className="text-xs sm:text-sm text-gray-500">
                          {checkedInCount}/{attendees.length} checked in
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="mt-4 sm:mt-6 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                        <button
                          onClick={() => handleDashboardView(event)}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                          title="View Dashboard"
                        >
                          <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                          <span className="hidden sm:inline">Dashboard</span>
                          <span className="sm:hidden">Stats</span>
                        </button>
                        <button
                          onClick={() => handleScannerView(event)}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
                          title="Open Scanner"
                        >
                          <QrCode className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                          Scanner
                        </button>
                        <button
                          onClick={() => handleDoorlistView(event)}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                          title="View Doorlist"
                        >
                          <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                          <span className="hidden sm:inline">Doorlist</span>
                          <span className="sm:hidden">List</span>
                        </button>
                      </div>

                      {/* More Actions Dropdown */}
                      <div className="mt-2 sm:mt-3 relative">
                        <button
                          onClick={() =>
                            setDropdownOpen(
                              dropdownOpen === event.id ? null : event.id
                            )
                          }
                          className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <MoreVertical className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                          More Actions
                        </button>

                        {dropdownOpen === event.id && (
                          <div className="absolute right-0 mt-2 w-full bg-white rounded-md shadow-lg z-10 border">
                            <div className="py-1">
                              <button
                                onClick={() => handleViewEvent(event)}
                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </button>
                              <button
                                onClick={() => handleEditEvent(event)}
                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Event
                              </button>
                              <button
                                onClick={() => handleDeleteEvent(event.id)}
                                className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                Delete Event
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <EventModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        mode="create"
      />

      <EventModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
        }}
        mode="edit"
        event={selectedEvent}
      />

      <EventDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
      />

      <EventDashboardModal
        isOpen={isDashboardModalOpen}
        onClose={() => {
          setIsDashboardModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
      />

      <EventScannerModal
        isOpen={isScannerModalOpen}
        onClose={() => {
          setIsScannerModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
      />

      <DoorlistModal
        isOpen={isDoorlistModalOpen}
        onClose={() => {
          setIsDoorlistModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
      />
    </div>
  );
};

export default EventsPage;
