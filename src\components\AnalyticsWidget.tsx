import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { TrendingUp, Users, Calendar, CheckCircle, BarChart3 } from 'lucide-react';
import { useEventStore } from '../store/eventStore';
import { AnalyticsService } from '../services/analyticsService';

interface AnalyticsWidgetProps {
  className?: string;
  showCharts?: boolean;
}

const AnalyticsWidget = ({ className = '', showCharts = true }: AnalyticsWidgetProps) => {
  const { events, attendees } = useEventStore();
  const [analytics, setAnalytics] = useState<any>(null);

  useEffect(() => {
    if (events.length > 0 || attendees.length > 0) {
      const analyticsData = AnalyticsService.generateAnalytics(events, attendees);
      setAnalytics(analyticsData);
    }
  }, [events, attendees]);

  if (!analytics) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Quick Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-5 w-5 text-blue-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Events</p>
              <p className="text-lg font-semibold text-gray-900">{analytics.totalEvents}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-5 w-5 text-green-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Attendees</p>
              <p className="text-lg font-semibold text-gray-900">{analytics.totalAttendees}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-5 w-5 text-purple-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Check-ins</p>
              <p className="text-lg font-semibold text-gray-900">{analytics.totalCheckedIn}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-5 w-5 text-orange-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Rate</p>
              <p className="text-lg font-semibold text-gray-900">
                {analytics.overallCheckInRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      {showCharts && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Check-ins */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Recent Check-ins
            </h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={analytics.checkInTrends.slice(-5)}>
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Bar dataKey="checkIns" fill="#3B82F6" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Event Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Event Status</h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={analytics.eventStatusDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ status, percentage }) => `${status} ${percentage.toFixed(0)}%`}
                  >
                    {analytics.eventStatusDistribution.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}

      {/* Top Events Summary */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Top Events</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {analytics.topEvents.slice(0, 3).map((item: any, index: number) => (
              <div key={item.event.id} className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.event.name}
                  </p>
                  <p className="text-sm text-gray-500 truncate">{item.event.location}</p>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{item.attendeeCount}</p>
                    <p className="text-xs text-gray-500">Attendees</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{item.checkInCount}</p>
                    <p className="text-xs text-gray-500">Check-ins</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">
                      {item.checkInRate.toFixed(1)}%
                    </p>
                    <p className="text-xs text-gray-500">Rate</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsWidget;
