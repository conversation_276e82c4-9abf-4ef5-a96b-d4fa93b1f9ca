import { QrCodeIcon } from "lucide-react";
import BaseModal from "./BaseModal";
import type { Attendee, Event } from "../types";
import QRCodeDisplay from "./QRCodeDisplay";
import { getModalZIndex } from "../utils/zIndex";

interface QRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  attendee: Attendee | null;
  event: Event | null;
}

const QRCodeModal = ({
  isOpen,
  onClose,
  attendee,
  event,
}: QRCodeModalProps) => {
  if (!isOpen || !attendee || !event) return null;

  const handleEmailSent = () => {
    // Could update attendee status or show notification
    console.log("QR code email sent successfully");
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="QR Code"
      subtitle={event.name}
      icon={<QrCodeIcon className="h-6 w-6 text-blue-600" />}
      size="lg"
      zIndex={getModalZIndex("QR_CODE_MODAL")}
    >
      <div className="mt-4">
        <QRCodeDisplay
          attendee={attendee}
          event={event}
          size="medium"
          showActions={true}
          onEmailSent={handleEmailSent}
        />
      </div>

      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">
          How to use this QR code:
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Save the QR code image to your device</li>
          <li>• Present it at the event entrance for scanning</li>
          <li>• The QR code contains encrypted attendee information</li>
          <li>• Each QR code is unique and cannot be duplicated</li>
          <li>• QR codes expire 24 hours after generation</li>
        </ul>
      </div>

      <div className="flex justify-end mt-6">
        <button
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Close
        </button>
      </div>
    </BaseModal>
  );
};

export default QRCodeModal;
